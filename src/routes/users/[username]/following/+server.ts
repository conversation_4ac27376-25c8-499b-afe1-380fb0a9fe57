import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { user, follow } from '$lib/server/db/schema';
import { eq, and, isNull, desc } from 'drizzle-orm';
import { getFederationConfig } from '$lib/activitypub/config/federation';
import type { RequestHandler } from './$types';

const ITEMS_PER_PAGE = 20;

export const GET: RequestHandler = async ({ params, url, request }) => {
  const username = params.username;

  if (!username) {
    return new Response('User not found', { status: 404 });
  }

  // Check if this is an ActivityPub request
  const acceptHeader = request.headers.get('accept') || '';
  const isActivityPubRequest =
    acceptHeader.includes('application/activity+json') ||
    acceptHeader.includes('application/ld+json') ||
    acceptHeader.includes('application/json');

  if (!isActivityPubRequest) {
    return new Response('Not Acceptable', { status: 406 });
  }

  try {
    // Find the local user
    const localUser = await db.query.user.findFirst({
      where: and(
        eq(user.username, username),
        isNull(user.domain) // только локальные пользователи
      )
    });

    if (!localUser) {
      return new Response('User not found', { status: 404 });
    }

    const federationConfig = getFederationConfig();
    const userUri = `${federationConfig.baseUrl}/users/${username}`;
    const followingUri = `${userUri}/following`;

    // Get pagination parameters
    const page = url.searchParams.get('page');
    const minId = url.searchParams.get('min_id');
    const maxId = url.searchParams.get('max_id');

    // If no pagination requested, return the collection metadata
    if (!page && !minId && !maxId) {
      // Get total count of accepted following
      const totalFollowingResult = await db
        .select()
        .from(follow)
        .where(and(
          eq(follow.followerId, localUser.id),
          eq(follow.status, 'accepted')
        ));

      const totalItems = totalFollowingResult.length;

      return json({
        '@context': 'https://www.w3.org/ns/activitystreams',
        id: followingUri,
        type: 'OrderedCollection',
        totalItems,
        first: totalItems > 0 ? `${followingUri}?page=1` : undefined,
        last: totalItems > 0 ? `${followingUri}?page=${Math.ceil(totalItems / ITEMS_PER_PAGE)}` : undefined
      }, {
        headers: {
          'Content-Type': 'application/activity+json; charset=utf-8',
          'Cache-Control': 'public, max-age=300', // 5 minutes cache
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Accept, Content-Type, Authorization'
        }
      });
    }

    // Handle paginated requests
    let pageNum = 1;
    if (page) {
      pageNum = Math.max(1, parseInt(page) || 1);
    }

    const offset = (pageNum - 1) * ITEMS_PER_PAGE;

    // Get following with pagination
    const following = await db
      .select({
        followingUri: follow.followingUri,
        followingUser: user
      })
      .from(follow)
      .innerJoin(user, eq(follow.followingUri, user.uri))
      .where(and(
        eq(follow.followerId, localUser.id),
        eq(follow.status, 'accepted')
      ))
      .orderBy(desc(follow.acceptedAt))
      .limit(ITEMS_PER_PAGE)
      .offset(offset);

    // Get total count for pagination
    const totalFollowingResult = await db
      .select({ count: follow.id })
      .from(follow)
      .where(and(
        eq(follow.followerId, localUser.id),
        eq(follow.status, 'accepted')
      ));

    const totalItems = totalFollowingResult.length;
    const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);

    // Build ordered items (actor URIs)
    const orderedItems = following.map(f => f.followingUri);

    // Build pagination links
    const pageResponse: any = {
      '@context': 'https://www.w3.org/ns/activitystreams',
      id: `${followingUri}?page=${pageNum}`,
      type: 'OrderedCollectionPage',
      partOf: followingUri,
      orderedItems,
      totalItems
    };

    // Add next/prev links
    if (pageNum > 1) {
      pageResponse.prev = `${followingUri}?page=${pageNum - 1}`;
    }
    if (pageNum < totalPages) {
      pageResponse.next = `${followingUri}?page=${pageNum + 1}`;
    }

    return json(pageResponse, {
      headers: {
        'Content-Type': 'application/activity+json; charset=utf-8',
        'Cache-Control': 'public, max-age=300', // 5 minutes cache
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Accept, Content-Type, Authorization'
      }
    });

  } catch (error) {
    console.error('Error serving following collection:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
};

export const OPTIONS: RequestHandler = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Accept, Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    }
  });
};
