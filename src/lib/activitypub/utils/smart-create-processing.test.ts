import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  isEmbeddedObject,
  separateEmbeddedAndReferencedObjects,
  processCreateActivitySmart,
  ActivityProcessors
} from './activities.js';
import type { Create } from '$lib/activitypub/types';

// Mock dependencies
vi.mock('$lib/activitypub/posts', () => ({
  importPost: vi.fn(),
  importPostWithAuth: vi.fn()
}));

vi.mock('./logger', () => ({
  ActivityPubLogs: {
    federation: {
      requestSent: vi.fn(),
      requestCompleted: vi.fn(),
      objectProcessed: vi.fn()
    },
    security: {
      blockedRequest: vi.fn(),
      signatureVerificationFailed: vi.fn()
    }
  }
}));

describe('Smart Create Activity Processing', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('isEmbeddedObject', () => {
    it('should return true for complete embedded objects', () => {
      const embeddedNote = {
        type: 'Note',
        id: 'https://example.com/notes/1',
        content: 'Hello world!',
        attributedTo: 'https://example.com/users/alice',
        published: '2023-01-01T00:00:00Z'
      };

      expect(isEmbeddedObject(embeddedNote)).toBe(true);
    });

    it('should return true for objects with type and attributedTo but no content', () => {
      const embeddedObject = {
        type: 'Article',
        id: 'https://example.com/articles/1',
        attributedTo: 'https://example.com/users/bob',
        name: 'Article Title'
      };

      expect(isEmbeddedObject(embeddedObject)).toBe(true);
    });

    it('should return false for URL references', () => {
      expect(isEmbeddedObject('https://example.com/notes/1')).toBe(false);
      expect(isEmbeddedObject(new URL('https://example.com/notes/1'))).toBe(false);
    });

    it('should return false for incomplete objects', () => {
      const incompleteObject = {
        type: 'Note',
        id: 'https://example.com/notes/1'
        // Missing content, attributedTo, and actor
      };

      expect(isEmbeddedObject(incompleteObject)).toBe(false);
    });

    it('should return false for objects without type', () => {
      const objectWithoutType = {
        id: 'https://example.com/notes/1',
        content: 'Hello world!',
        attributedTo: 'https://example.com/users/alice'
      };

      expect(isEmbeddedObject(objectWithoutType)).toBe(false);
    });

    it('should handle null and undefined gracefully', () => {
      expect(isEmbeddedObject(null)).toBe(false);
      expect(isEmbeddedObject(undefined)).toBe(false);
      expect(isEmbeddedObject('')).toBe(false);
      expect(isEmbeddedObject(123)).toBe(false);
    });
  });

  describe('separateEmbeddedAndReferencedObjects', () => {
    it('should separate embedded objects from URL references', () => {
      const activity: Create = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/1',
        actor: 'https://example.com/users/alice',
        object: [
          // Embedded object
          {
            type: 'Note',
            id: 'https://example.com/notes/1',
            content: 'Embedded note',
            attributedTo: 'https://example.com/users/alice'
          },
          // URL reference
          'https://example.com/notes/2',
          // Another embedded object
          {
            type: 'Article',
            id: 'https://example.com/articles/1',
            name: 'Article Title',
            attributedTo: 'https://example.com/users/alice'
          }
        ]
      };

      const result = separateEmbeddedAndReferencedObjects(activity);

      expect(result.embedded).toHaveLength(2);
      expect(result.references).toHaveLength(1);
      expect(result.references[0]).toBe('https://example.com/notes/2');
      expect(result.embedded[0].type).toBe('Note');
      expect(result.embedded[1].type).toBe('Article');
    });

    it('should handle single object (not array)', () => {
      const activity: Create = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/1',
        actor: 'https://example.com/users/alice',
        object: {
          type: 'Note',
          id: 'https://example.com/notes/1',
          content: 'Single embedded note',
          attributedTo: 'https://example.com/users/alice'
        }
      };

      const result = separateEmbeddedAndReferencedObjects(activity);

      expect(result.embedded).toHaveLength(1);
      expect(result.references).toHaveLength(0);
      expect(result.embedded[0].content).toBe('Single embedded note');
    });

    it('should handle activity with no objects', () => {
      const activity: Create = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/1',
        actor: 'https://example.com/users/alice'
        // No object property
      };

      const result = separateEmbeddedAndReferencedObjects(activity);

      expect(result.embedded).toHaveLength(0);
      expect(result.references).toHaveLength(0);
    });
  });

  describe('processCreateActivitySmart', () => {
    it('should process embedded objects and URL references correctly', async () => {
      const mockProcessEmbedded = vi.fn().mockResolvedValue(undefined);
      const mockProcessUrl = vi.fn().mockResolvedValue(undefined);

      const activity: Create = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/1',
        actor: 'https://example.com/users/alice',
        object: [
          {
            type: 'Note',
            id: 'https://example.com/notes/1',
            content: 'Embedded note',
            attributedTo: 'https://example.com/users/alice'
          },
          'https://example.com/notes/2'
        ]
      };

      const result = await processCreateActivitySmart(
        activity,
        mockProcessEmbedded,
        mockProcessUrl
      );

      expect(mockProcessEmbedded).toHaveBeenCalledTimes(1);
      expect(mockProcessUrl).toHaveBeenCalledTimes(1);
      expect(mockProcessUrl).toHaveBeenCalledWith('https://example.com/notes/2');
      expect(result.processed).toBe(2);
      expect(result.failed).toBe(0);
      expect(result.embeddedCount).toBe(1);
      expect(result.fetchedCount).toBe(1);
    });

    it('should handle processing failures gracefully', async () => {
      const mockProcessEmbedded = vi.fn().mockRejectedValue(new Error('Processing failed'));
      const mockProcessUrl = vi.fn().mockResolvedValue(undefined);

      const activity: Create = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/1',
        actor: 'https://example.com/users/alice',
        object: [
          {
            type: 'Note',
            id: 'https://example.com/notes/1',
            content: 'Embedded note',
            attributedTo: 'https://example.com/users/alice'
          },
          'https://example.com/notes/2'
        ]
      };

      const result = await processCreateActivitySmart(
        activity,
        mockProcessEmbedded,
        mockProcessUrl
      );

      expect(result.processed).toBe(1);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('Processing failed');
    });

    it('should handle mixed success and failure scenarios', async () => {
      const mockProcessEmbedded = vi.fn().mockResolvedValue(undefined);
      const mockProcessUrl = vi.fn()
        .mockResolvedValueOnce(undefined) // First URL succeeds
        .mockRejectedValueOnce(new Error('Network timeout')); // Second URL fails

      const activity: Create = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/1',
        actor: 'https://example.com/users/alice',
        object: [
          {
            type: 'Note',
            id: 'https://example.com/notes/1',
            content: 'Embedded note',
            attributedTo: 'https://example.com/users/alice'
          },
          'https://example.com/notes/2',
          'https://example.com/notes/3'
        ]
      };

      const result = await processCreateActivitySmart(
        activity,
        mockProcessEmbedded,
        mockProcessUrl
      );

      expect(result.processed).toBe(2); // 1 embedded + 1 successful URL
      expect(result.failed).toBe(1); // 1 failed URL
      expect(result.embeddedCount).toBe(1);
      expect(result.fetchedCount).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('Network timeout');
    });
  });

  describe('ActivityProcessors.processCreateSmart', () => {
    it('should use the smart processor correctly', async () => {
      const mockImportEmbedded = vi.fn().mockResolvedValue(undefined);
      const mockImportUrl = vi.fn().mockResolvedValue(undefined);

      const activity: Create = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/1',
        actor: 'https://example.com/users/alice',
        object: {
          type: 'Note',
          id: 'https://example.com/notes/1',
          content: 'Test note',
          attributedTo: 'https://example.com/users/alice'
        }
      };

      const result = await ActivityProcessors.processCreateSmart(
        activity,
        mockImportEmbedded,
        mockImportUrl
      );

      expect(mockImportEmbedded).toHaveBeenCalledTimes(1);
      expect(mockImportUrl).toHaveBeenCalledTimes(0);
      expect(result.processed).toBe(1);
      expect(result.embeddedCount).toBe(1);
      expect(result.fetchedCount).toBe(0);
    });
  });
});
