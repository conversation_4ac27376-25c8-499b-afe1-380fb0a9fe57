import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock dependencies first
vi.mock('./utils/fetch', () => ({
  fetchAPObjectWithRetry: vi.fn()
}));

vi.mock('./utils/logger', () => ({
  ActivityPubLogs: {
    federation: {
      requestSent: vi.fn(),
      requestCompleted: vi.fn(),
      objectProcessed: vi.fn()
    },
    security: {
      blockedRequest: vi.fn(),
      signatureVerificationFailed: vi.fn()
    }
  }
}));

// Mock the importPost function
vi.mock('./posts', () => ({
  importPost: vi.fn(),
  importPostWithAuth: vi.fn()
}));

// Now import the modules
import { importPostWithAuth } from './posts.js';
import { importPost } from './posts.js';
import { fetchAPObjectWithRetry } from './utils/fetch';
import { ActivityPubLogs } from './utils/logger';

describe('importPostWithAuth', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should successfully import a public post', async () => {
    const mockPost = {
      type: 'Note',
      id: 'https://example.com/notes/1',
      content: 'Hello world!',
      attributedTo: 'https://example.com/users/alice'
    };

    vi.mocked(fetchAPObjectWithRetry).mockResolvedValue({
      success: true,
      result: mockPost
    });

    vi.mocked(importPost).mockResolvedValue({ id: 'imported-post-id' });

    const result = await importPostWithAuth('https://example.com/notes/1');

    expect(fetchAPObjectWithRetry).toHaveBeenCalledWith('https://example.com/notes/1', {
      timeout: 15000
    });
    expect(importPost).toHaveBeenCalledWith(mockPost);
    expect(ActivityPubLogs.federation.requestSent).toHaveBeenCalledWith(
      'https://example.com/notes/1',
      'authenticated'
    );
    expect(ActivityPubLogs.federation.requestCompleted).toHaveBeenCalledWith(
      'https://example.com/notes/1',
      'authenticated',
      'success'
    );
    expect(result).toEqual({ id: 'imported-post-id' });
  });

  it('should handle 404 errors for deleted/private posts', async () => {
    vi.mocked(fetchAPObjectWithRetry).mockResolvedValue({
      success: false,
      statusCode: 404,
      error: 'Not Found'
    });

    await expect(importPostWithAuth('https://example.com/notes/1')).rejects.toThrow(
      'Post not found: https://example.com/notes/1 (404)'
    );

    expect(ActivityPubLogs.security.blockedRequest).toHaveBeenCalledWith(
      'https://example.com/notes/1',
      'Post not found (404) - may be deleted or private'
    );
  });

  it('should handle 403 errors for private posts', async () => {
    vi.mocked(fetchAPObjectWithRetry).mockResolvedValue({
      success: false,
      statusCode: 403,
      error: 'Forbidden'
    });

    await expect(importPostWithAuth('https://example.com/notes/1')).rejects.toThrow(
      'Access forbidden to private post: https://example.com/notes/1 (403)'
    );

    expect(ActivityPubLogs.security.blockedRequest).toHaveBeenCalledWith(
      'https://example.com/notes/1',
      'Access forbidden (403) - private post or server restriction'
    );
  });

  it('should handle 401 authentication errors', async () => {
    vi.mocked(fetchAPObjectWithRetry).mockResolvedValue({
      success: false,
      statusCode: 401,
      error: 'Unauthorized'
    });

    await expect(importPostWithAuth('https://example.com/notes/1')).rejects.toThrow(
      'Authentication failed for post: https://example.com/notes/1 (401)'
    );

    expect(ActivityPubLogs.security.signatureVerificationFailed).toHaveBeenCalledWith(
      'service',
      'https://example.com/notes/1',
      'Authentication failed'
    );
  });

  it('should handle server errors (5xx)', async () => {
    vi.mocked(fetchAPObjectWithRetry).mockResolvedValue({
      success: false,
      statusCode: 500,
      error: 'Internal Server Error'
    });

    await expect(importPostWithAuth('https://example.com/notes/1')).rejects.toThrow(
      'Server error while fetching post: Internal Server Error (500)'
    );

    expect(ActivityPubLogs.federation.requestCompleted).toHaveBeenCalledWith(
      'https://example.com/notes/1',
      'authenticated',
      'server_error'
    );
  });

  it('should handle post processing failures', async () => {
    const mockPost = {
      type: 'Note',
      id: 'https://example.com/notes/1',
      content: 'Hello world!',
      attributedTo: 'https://example.com/users/alice'
    };

    vi.mocked(fetchAPObjectWithRetry).mockResolvedValue({
      success: true,
      result: mockPost
    });

    vi.mocked(importPost).mockRejectedValue(new Error('Database connection failed'));

    await expect(importPostWithAuth('https://example.com/notes/1')).rejects.toThrow(
      'Failed to process fetched post: Database connection failed'
    );

    expect(ActivityPubLogs.federation.objectProcessed).toHaveBeenCalledWith(
      'https://example.com/notes/1',
      'post',
      'failed'
    );
  });

  it('should handle network timeouts and other errors', async () => {
    vi.mocked(fetchAPObjectWithRetry).mockResolvedValue({
      success: false,
      error: 'Network timeout',
      statusCode: undefined
    });

    await expect(importPostWithAuth('https://example.com/notes/1')).rejects.toThrow(
      'Failed to fetch post: Network timeout (unknown)'
    );

    expect(ActivityPubLogs.federation.requestCompleted).toHaveBeenCalledWith(
      'https://example.com/notes/1',
      'authenticated',
      'failed'
    );
  });

  it('should handle unexpected errors gracefully', async () => {
    vi.mocked(fetchAPObjectWithRetry).mockRejectedValue(new Error('Unexpected error'));

    await expect(importPostWithAuth('https://example.com/notes/1')).rejects.toThrow(
      'Failed to import post from https://example.com/notes/1: Error: Unexpected error'
    );
  });
});
