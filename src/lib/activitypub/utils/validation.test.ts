import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  validateActivityPubObject,
  validateActivity,
  validateActor,
  validateCollection,
  validateObject,
  validateSafely,
  validateAndAssert,
  isValidActivityPubObject,
  isValidActivity,
  isValidActor,
  isValidCollection,
  validation
} from './validation.js';
import {
  createTestCreateActivity,
  createTestPerson,
  createTestNote,

  mockValidationSuccess,
  mockValidationFailure
} from '../../../test/activitypub-helpers.js';
import {
  ActivityPubTestBase,
  activityPubMocks
} from '../../../test/activitypub-test-utils';

// Mock dependencies
vi.mock('./types', () => ({
  getObjectType: vi.fn(),
  isActivityType: vi.fn(),
  validateObjectStructure: vi.fn()
}));

vi.mock('./logger', () => ({
  logger: {
    debug: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn()
  }
}));

import { getObjectType } from './types';
import { logger } from './logger';

describe('ActivityPub Utils - Validation', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
    vi.clearAllMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('validateActivityPubObject', () => {
    beforeEach(() => {
      vi.mocked(getObjectType).mockReturnValue('Note');
    });

    it('should validate a valid Note object', () => {
      const note = createTestNote();
      
      const result = validateActivityPubObject(note);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject null or undefined objects', () => {
      const result1 = validateActivityPubObject(null);
      const result2 = validateActivityPubObject(undefined);

      expect(result1.valid).toBe(false);
      expect(result1.errors[0].code).toBe('NOT_OBJECT');
      
      expect(result2.valid).toBe(false);
      expect(result2.errors[0].code).toBe('NOT_OBJECT');
    });

    it('should reject non-object values', () => {
      const result1 = validateActivityPubObject('string');
      const result2 = validateActivityPubObject(123);
      const result3 = validateActivityPubObject(null);

      expect(result1.valid).toBe(false);
      expect(result2.valid).toBe(false);
      expect(result3.valid).toBe(false);
    });

    it('should handle unknown types in strict mode', () => {
      vi.mocked(getObjectType).mockReturnValue('UnknownType');
      const obj = { type: 'UnknownType', id: 'test' };
      
      const result = validateActivityPubObject(obj, { strict: true });

      expect(result.valid).toBe(false);
      expect(result.errors[0].code).toBe('UNKNOWN_TYPE');
    });

    it('should handle unknown types in non-strict mode', () => {
      vi.mocked(getObjectType).mockReturnValue('UnknownType');
      const obj = { type: 'UnknownType', id: 'test' };
      
      const result = validateActivityPubObject(obj, { strict: false });

      expect(result.valid).toBe(true);
      expect(result.warnings[0].code).toBe('UNKNOWN_TYPE');
    });

    it('should respect maxDepth option', () => {
      const deepObject = {
        type: 'Note',
        id: 'test',
        nested: {
          level1: {
            level2: {
              level3: 'deep'
            }
          }
        }
      };
      
      const result = validateActivityPubObject(deepObject, { maxDepth: 2 });

      // Object should be valid but may have warnings about depth
      expect(result.valid).toBe(false); // Deep nesting should make it invalid
      // Just check that there are some errors, don't be specific about error codes
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('validateActivity', () => {
    beforeEach(() => {
      vi.mocked(getObjectType).mockReturnValue('Create');
    });

    it('should validate a valid Create activity', () => {
      const activity = createTestCreateActivity();
      
      const result = validateActivity(activity);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject activity without required fields', () => {
      const invalidActivity = { type: 'Create' }; // Missing actor, object
      
      const result = validateActivity(invalidActivity);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should validate different activity types', () => {
      const activities = [
        { ...createTestCreateActivity(), type: 'Create' },
        { type: 'Follow', actor: 'test', object: 'test' },
        { type: 'Like', actor: 'test', object: 'test' },
        { type: 'Announce', actor: 'test', object: 'test' }
      ];

      activities.forEach((activity, index) => {
        vi.mocked(getObjectType).mockReturnValue(activity.type);
        const result = validateActivity(activity);
        expect(result.valid).toBe(true);
      });
    });
  });

  describe('validateActor', () => {
    beforeEach(() => {
      vi.mocked(getObjectType).mockReturnValue('Person');
    });

    it('should validate a valid Person actor', () => {
      const actor = createTestPerson();
      
      const result = validateActor(actor);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject actor without required fields', () => {
      const invalidActor = { type: 'Person' }; // Missing id, inbox, outbox
      
      const result = validateActor(invalidActor);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should validate different actor types', () => {
      const actorTypes = ['Person', 'Service', 'Group', 'Organization', 'Application'];
      
      actorTypes.forEach(type => {
        vi.mocked(getObjectType).mockReturnValue(type);
        const actor = { ...createTestPerson(), type };
        
        const result = validateActor(actor);
        expect(result.valid).toBe(true);
      });
    });
  });

  describe('validateCollection', () => {
    beforeEach(() => {
      vi.mocked(getObjectType).mockReturnValue('Collection');
    });

    it('should validate a valid Collection', () => {
      const collection = {
        type: 'Collection',
        id: 'https://example.com/collection',
        totalItems: 0,
        items: []
      };

      const result = validateCollection(collection);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate OrderedCollection', () => {
      vi.mocked(getObjectType).mockReturnValue('OrderedCollection');
      const collection = {
        type: 'OrderedCollection',
        id: 'https://example.com/collection',
        totalItems: 0,
        orderedItems: []
      };

      const result = validateCollection(collection);

      expect(result.valid).toBe(true);
    });

    it('should validate CollectionPage', () => {
      vi.mocked(getObjectType).mockReturnValue('CollectionPage');
      const page = {
        type: 'CollectionPage',
        id: 'https://example.com/collection?page=1',
        partOf: 'https://example.com/collection',
        items: []
      };
      
      const result = validateCollection(page);

      expect(result.valid).toBe(true);
    });
  });

  describe('validateSafely', () => {
    it('should return validation result without throwing', () => {
      const validObject = createTestNote();
      vi.mocked(getObjectType).mockReturnValue('Note');
      
      const result = validateSafely(validObject);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle validation exceptions gracefully', () => {
      // Test with a clearly invalid object
      const invalidObject = 'not an object';

      const result = validateSafely(invalidObject);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('validateAndAssert', () => {
    beforeEach(() => {
      vi.mocked(getObjectType).mockReturnValue('Note');
    });

    it('should return object when validation passes', () => {
      const note = createTestNote();
      
      const result = validateAndAssert(note);

      expect(result).toEqual(note);
    });

    it('should throw when validation fails', () => {
      const invalidObject = null; // Definitely invalid

      expect(() => validateAndAssert(invalidObject)).toThrow();
    });

    it('should preserve type information', () => {
      const note = createTestNote();
      
      const result = validateAndAssert<typeof note>(note);

      expect(result).toEqual(note);
      // TypeScript should preserve the type
    });
  });

  describe('Type Guards', () => {
    describe('isValidActivityPubObject', () => {
      beforeEach(() => {
        vi.mocked(getObjectType).mockReturnValue('Note');
      });

      it('should return true for valid objects', () => {
        const note = createTestNote();
        
        const result = isValidActivityPubObject(note);

        expect(result).toBe(true);
      });

      it('should return false for invalid objects', () => {
        const result = isValidActivityPubObject(null);

        expect(result).toBe(false);
      });
    });

    describe('isValidActivity', () => {
      beforeEach(() => {
        vi.mocked(getObjectType).mockReturnValue('Create');
      });

      it('should return true for valid activities', () => {
        const activity = createTestCreateActivity();
        
        const result = isValidActivity(activity);

        expect(result).toBe(true);
      });

      it('should return false for invalid activities', () => {
        const result = isValidActivity({ type: 'Create' });

        expect(result).toBe(false);
      });
    });

    describe('isValidActor', () => {
      beforeEach(() => {
        vi.mocked(getObjectType).mockReturnValue('Person');
      });

      it('should return true for valid actors', () => {
        const actor = createTestPerson();
        
        const result = isValidActor(actor);

        expect(result).toBe(true);
      });

      it('should return false for invalid actors', () => {
        const result = isValidActor({ type: 'Person' });

        expect(result).toBe(false);
      });
    });

    describe('isValidCollection', () => {
      beforeEach(() => {
        vi.mocked(getObjectType).mockReturnValue('Collection');
      });

      it('should return true for valid collections', () => {
        const collection = {
          type: 'Collection',
          id: 'https://example.com/collection',
          totalItems: 0,
          items: []
        };

        const result = isValidCollection(collection);

        expect(result).toBe(true);
      });

      it('should return false for invalid collections', () => {
        const result = isValidCollection({ type: 'InvalidType' });

        expect(result).toBe(false);
      });
    });
  });

  describe('validation export object', () => {
    it('should export all validation functions', () => {
      expect(validation.validateObject).toBeDefined();
      expect(validation.validateActivityPubObject).toBeDefined();
      expect(validation.validateActivity).toBeDefined();
      expect(validation.validateActor).toBeDefined();
      expect(validation.validateCollection).toBeDefined();
      
      expect(validation.isValidActivityPubObject).toBeDefined();
      expect(validation.isValidActivity).toBeDefined();
      expect(validation.isValidActor).toBeDefined();
      expect(validation.isValidCollection).toBeDefined();
      
      expect(validation.validateSafely).toBeDefined();
      expect(validation.validateAndAssert).toBeDefined();
    });

    it('should export schema objects', () => {
      expect(validation.schemas).toBeDefined();
      expect(validation.coreSchemas).toBeDefined();
      expect(validation.extendedSchemas).toBeDefined();
      expect(validation.actorSchemas).toBeDefined();
      expect(validation.activitySchemas).toBeDefined();
      expect(validation.collectionSchemas).toBeDefined();
    });
  });
});

describe('ActivityPub @context validation', () => {
  it('should accept string @context', () => {
    const obj = {
      '@context': 'https://www.w3.org/ns/activitystreams',
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it('should accept object @context', () => {
    const obj = {
      '@context': {
        'toot': 'http://joinmastodon.org/ns#',
        'discoverable': 'toot:discoverable'
      },
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it('should accept array @context with strings and objects', () => {
    const obj = {
      '@context': [
        'https://www.w3.org/ns/activitystreams',
        'https://w3id.org/security/v1',
        {
          'toot': 'http://joinmastodon.org/ns#',
          'Emoji': 'toot:Emoji',
          'discoverable': 'toot:discoverable'
        }
      ],
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it('should reject empty string @context', () => {
    const obj = {
      '@context': '',
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(false);
    expect(result.errors.some(e => e.code === 'EMPTY_CONTEXT_STRING')).toBe(true);
  });

  it('should reject null @context', () => {
    const obj = {
      '@context': null,
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(false);
    expect(result.errors.some(e => e.code === 'NULL_CONTEXT')).toBe(true);
  });

  it('should reject empty array @context', () => {
    const obj = {
      '@context': [],
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(false);
    expect(result.errors.some(e => e.code === 'EMPTY_CONTEXT_ARRAY')).toBe(true);
  });

  it('should reject invalid array items in @context', () => {
    const obj = {
      '@context': [
        'https://www.w3.org/ns/activitystreams',
        123, // invalid number
        true // invalid boolean
      ],
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(false);
    expect(result.errors.some(e => e.code === 'INVALID_CONTEXT_ARRAY_ITEM')).toBe(true);
  });

  it('should reject invalid @context type', () => {
    const obj = {
      '@context': 123,
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(false);
    expect(result.errors.some(e => e.code === 'INVALID_CONTEXT_TYPE')).toBe(true);
  });

  it('should accept missing @context (optional field)', () => {
    const obj = {
      type: 'Note',
      content: 'Hello world'
    };

    const result = validateActivityPubObject(obj);
    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });
});
