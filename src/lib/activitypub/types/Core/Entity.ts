import type { TypeOrArrayWithType, AnyType, OrArray } from '../util';

/**
 * JSON-LD Context can be a string (URL), an object with term definitions,
 * or an array containing both strings and objects.
 *
 * @see https://www.w3.org/TR/json-ld/#the-context
 * @see https://docs.joinmastodon.org/spec/activitypub/#contexts
 */
export type JsonLdContext = string | Record<string, any> | Array<string | Record<string, any>>;

/**
 * A base ActivityStreams Entity is a plain object that has at least a `type`
 * property.
 *
 * The @context property follows JSON-LD specification and can contain:
 * - A string URL pointing to a context document
 * - An object with term definitions and mappings
 * - An array containing strings and/or objects
 */
export type BaseEntity<T extends AnyType> = {
  '@context'?: JsonLdContext;
  // Activity Pub allows null.
  id?: URL | null;
  type: T | TypeOrArrayWithType<T>;
};
