/**
 * Utilities for validating and processing ActivityPub activities
 */

import type {
  Activity,
  Create,
  Update,
  Delete,
  Follow,
  Accept,
  Reject,
  Add,
  Remove,
  Like,
  Announce,
  Undo,
  Block,
  Flag,
  EntityReference
} from '$lib/activitypub/types';
import {
  getObjectType,
  isActivityType,
  type ValidationResult
} from './types';
import { validateUrl } from './links';
import {
  ValidationError,
  MissingFieldError,
  InvalidTypeError,
  MalformedObjectError
} from './errors';
import { discoverActor } from '$lib/activitypub/actors';

/**
 * Activity type guards
 */
export function isCreateActivity(activity: Activity): activity is Create {
  return getObjectType(activity) === 'Create';
}

export function isUpdateActivity(activity: Activity): activity is Update {
  return getObjectType(activity) === 'Update';
}

export function isDeleteActivity(activity: Activity): activity is Delete {
  return getObjectType(activity) === 'Delete';
}

export function isFollowActivity(activity: Activity): activity is Follow {
  return getObjectType(activity) === 'Follow';
}

export function isAcceptActivity(activity: Activity): activity is Accept {
  return getObjectType(activity) === 'Accept';
}

export function isRejectActivity(activity: Activity): activity is Reject {
  return getObjectType(activity) === 'Reject';
}

export function isAddActivity(activity: Activity): activity is Add {
  return getObjectType(activity) === 'Add';
}

export function isRemoveActivity(activity: Activity): activity is Remove {
  return getObjectType(activity) === 'Remove';
}

export function isLikeActivity(activity: Activity): activity is Like {
  return getObjectType(activity) === 'Like';
}

export function isAnnounceActivity(activity: Activity): activity is Announce {
  return getObjectType(activity) === 'Announce';
}

export function isUndoActivity(activity: Activity): activity is Undo {
  return getObjectType(activity) === 'Undo';
}

export function isBlockActivity(activity: Activity): activity is Block {
  return getObjectType(activity) === 'Block';
}

export function isFlagActivity(activity: Activity): activity is Flag {
  return getObjectType(activity) === 'Flag';
}

/**
 * Check if an object is a valid activity
 */
export function isValidActivity(obj: any): obj is Activity {
  if (!obj || typeof obj !== 'object') {
    return false;
  }

  const type = getObjectType(obj);
  if (!isActivityType(type)) {
    return false;
  }

  // Check required fields for activities
  if (!obj.actor) {
    return false;
  }

  return true;
}

/**
 * Validate activity structure
 */
export function validateActivity(activity: any): ValidationResult {
  const errors: string[] = [];

  // Basic object validation
  if (!activity || typeof activity !== 'object') {
    errors.push('Activity must be an object');
    return { isValid: false, errors };
  }

  // Type validation
  const type = getObjectType(activity);
  if (!type) {
    errors.push('Activity missing type field');
  } else if (!isActivityType(type)) {
    errors.push(`Invalid activity type: ${type}`);
  }

  // Actor validation
  if (!activity.actor) {
    errors.push('Activity missing required field: actor');
  } else if (!isValidActivityReference(activity.actor)) {
    errors.push('Activity actor must be a valid reference');
  }

  // ID validation
  if (activity.id && !validateUrl(activity.id.toString()).isValid) {
    errors.push('Activity ID must be a valid URL');
  }

  // Object validation for activities that require it
  if (requiresObject(type) && !activity.object) {
    errors.push(`Activity type ${type} requires an object field`);
  }

  // Target validation for activities that require it
  if (requiresTarget(type) && !activity.target) {
    errors.push(`Activity type ${type} requires a target field`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Check if activity type requires an object field
 */
function requiresObject(type: string): boolean {
  return [
    'Create', 'Update', 'Delete', 'Follow', 'Accept', 'Reject',
    'Like', 'Announce', 'Undo', 'Block', 'Flag'
  ].includes(type);
}

/**
 * Check if activity type requires a target field
 */
function requiresTarget(type: string): boolean {
  return ['Add', 'Remove'].includes(type);
}

/**
 * Check if a reference is valid (URL or object with id)
 */
function isValidActivityReference(ref: EntityReference): boolean {
  if (typeof ref === 'string') {
    return validateUrl(ref).isValid;
  }

  if (ref instanceof URL) {
    return validateUrl(ref.toString()).isValid;
  }

  if (typeof ref === 'object' && ref.id) {
    return validateUrl(ref.id.toString()).isValid;
  }

  return false;
}

/**
 * Extract objects from activity safely
 */
export function extractActivityObjects(activity: Activity): EntityReference[] {
  if (!activity.object) {
    return [];
  }

  const objects = Array.isArray(activity.object) ? activity.object : [activity.object];
  return objects.filter(obj => isValidActivityReference(obj));
}

/**
 * Extract actor from activity safely
 */
export function extractActivityActor(activity: Activity): EntityReference | null {
  if (!activity.actor) {
    return null;
  }

  // Handle array of actors - take the first valid one
  if (Array.isArray(activity.actor)) {
    for (const actor of activity.actor) {
      if (isValidActivityReference(actor)) {
        return actor;
      }
    }
    return null;
  }

  return isValidActivityReference(activity.actor) ? activity.actor : null;
}

/**
 * Check if activity is transitive (has object) or intransitive (no object)
 */
export function isTransitiveActivity(activity: Activity): boolean {
  return !!activity.object;
}

export function isIntransitiveActivity(activity: Activity): boolean {
  return !activity.object;
}

/**
 * Get activity summary for logging/debugging
 */
export function getActivitySummary(activity: Activity): {
  type: string;
  actor: string | null;
  objectCount: number;
  hasTarget: boolean;
  isTransitive: boolean;
} {
  const type = getObjectType(activity);
  const actorRef = extractActivityActor(activity);
  const actor = actorRef ?
    (typeof actorRef === 'string' ? actorRef :
     actorRef instanceof URL ? actorRef.toString() :
     (actorRef as any).id?.toString() || null) : null;

  const objects = extractActivityObjects(activity);

  return {
    type,
    actor,
    objectCount: objects.length,
    hasTarget: !!activity.target,
    isTransitive: isTransitiveActivity(activity)
  };
}

/**
 * Validate and throw appropriate errors
 */
export function validateActivityOrThrow(activity: any): asserts activity is Activity {
  const validation = validateActivity(activity);

  if (!validation.isValid) {
    const firstError = validation.errors[0];

    if (firstError.includes('missing type')) {
      throw new MissingFieldError('type', 'Activity');
    }

    if (firstError.includes('Invalid activity type')) {
      const type = getObjectType(activity);
      throw new InvalidTypeError('valid activity type', type);
    }

    if (firstError.includes('missing required field')) {
      const field = firstError.split(': ')[1];
      throw new MissingFieldError(field, 'Activity');
    }

    throw new MalformedObjectError('Activity', validation.errors.join('; '));
  }
}

/**
 * Safe activity processing with validation
 */
export function processActivitySafely<T extends Activity = Activity>(
  rawActivity: any
): { success: true; activity: T } | { success: false; error: ValidationError } {
  try {
    validateActivityOrThrow(rawActivity);

    // Additional type safety check - ensure the activity has the expected structure
    if (!rawActivity || typeof rawActivity !== 'object' || !rawActivity.type) {
      return {
        success: false,
        error: new MalformedObjectError('Activity', 'Invalid activity structure after validation')
      };
    }

    // Safe type assertion after validation and structure check
    return { success: true, activity: rawActivity as T };
  } catch (error) {
    if (error instanceof ValidationError) {
      return { success: false, error };
    }

    return {
      success: false,
      error: new MalformedObjectError('Activity', error instanceof Error ? error.message : 'Unknown error')
    };
  }
}

/**
 * Check if an object is a complete embedded object (not just a reference)
 */
export function isEmbeddedObject(obj: any): boolean {
  try {
    if (!obj || typeof obj !== 'object') {
      return false;
    }

    // If it's just a URL object, it's not embedded
    if (obj instanceof URL) {
      return false;
    }

    // Check for essential properties that indicate a complete object
    // For posts/notes, we need at least type and content or attributedTo
    const hasType = obj.type && typeof obj.type === 'string';
    const hasContent = !!(obj.content || obj.name || obj.summary);
    const hasAttributedTo = !!obj.attributedTo;
    const hasActor = !!obj.actor;

    // Additional checks for object completeness
    const hasId = obj.id && typeof obj.id === 'string';

    // For ActivityPub objects, we consider it embedded if it has:
    // 1. A type (required)
    // 2. Either content OR attributedTo OR actor (indicates it's not just a stub)
    // 3. Preferably also has id and published (but not required)
    if (!hasType) {
      return false;
    }

    const isComplete = hasContent || hasAttributedTo || hasActor;

    // Log for debugging if object seems incomplete
    if (!isComplete) {
      console.debug(`Object with type ${obj.type} appears to be a reference (missing content/attributedTo/actor)`, {
        hasContent,
        hasAttributedTo,
        hasActor,
        hasId: !!hasId
      });
    }

    return isComplete;
  } catch (error) {
    console.warn(`Error checking if object is embedded:`, error);
    return false;
  }
}

/**
 * Separate embedded objects from URL references in activity objects
 */
export function separateEmbeddedAndReferencedObjects(activity: Activity): {
  embedded: any[];
  references: string[];
} {
  const objects = extractActivityObjects(activity);
  const embedded: any[] = [];
  const references: string[] = [];

  for (const obj of objects) {
    if (typeof obj === 'string') {
      if (validateUrl(obj).isValid) {
        references.push(obj);
      }
    } else if (obj instanceof URL) {
      references.push(obj.toString());
    } else if (typeof obj === 'object') {
      if (isEmbeddedObject(obj)) {
        // This is a complete embedded object
        embedded.push(obj);
      } else {
        // This is likely just a reference object, try to extract URL
        if (obj.id && validateUrl(obj.id.toString()).isValid) {
          references.push(obj.id.toString());
        } else if ('url' in obj && obj.url) {
          const urlStr = obj.url instanceof URL ? obj.url.toString() : obj.url.toString();
          if (validateUrl(urlStr).isValid) {
            references.push(urlStr);
          }
        }
      }
    }
  }

  return {
    embedded: [...new Set(embedded)], // Remove duplicates
    references: [...new Set(references)] // Remove duplicates
  };
}

/**
 * Extract URLs from activity objects for processing (legacy function)
 * @deprecated Use separateEmbeddedAndReferencedObjects instead for smart processing
 */
export function extractObjectUrls(activity: Activity): string[] {
  const objects = extractActivityObjects(activity);
  const urls: string[] = [];

  for (const obj of objects) {
    if (typeof obj === 'string') {
      if (validateUrl(obj).isValid) {
        urls.push(obj);
      }
    } else if (obj instanceof URL) {
      urls.push(obj.toString());
    } else if (typeof obj === 'object') {
      // Try to extract URL from object
      if (obj.id && validateUrl(obj.id.toString()).isValid) {
        urls.push(obj.id.toString());
      } else if ('url' in obj && obj.url) {
        const urlStr = obj.url instanceof URL ? obj.url.toString() : obj.url.toString();
        if (validateUrl(urlStr).isValid) {
          urls.push(urlStr);
        }
      }
    }
  }

  return [...new Set(urls)]; // Remove duplicates
}

/**
 * Process activity objects with error recovery
 */
export async function processActivityObjects(
  activity: Activity,
  processor: (url: string) => Promise<void>,
  options: {
    parallel?: boolean;
    continueOnError?: boolean;
    maxConcurrency?: number;
  } = {}
): Promise<{
  processed: number;
  failed: number;
  errors: string[];
}> {
  const { parallel = true, continueOnError = true, maxConcurrency = 5 } = options;
  const urls = extractObjectUrls(activity);

  let processed = 0;
  let failed = 0;
  const errors: string[] = [];

  if (parallel) {
    // Process in batches to avoid overwhelming the server
    const batches: string[][] = [];
    for (let i = 0; i < urls.length; i += maxConcurrency) {
      batches.push(urls.slice(i, i + maxConcurrency));
    }

    for (const batch of batches) {
      const results = await Promise.allSettled(
        batch.map(url => processor(url))
      );

      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        if (result.status === 'fulfilled') {
          processed++;
        } else {
          failed++;
          errors.push(`Failed to process ${batch[i]}: ${result.reason}`);

          if (!continueOnError) {
            return { processed, failed, errors };
          }
        }
      }
    }
  } else {
    // Sequential processing
    for (const url of urls) {
      try {
        await processor(url);
        processed++;
      } catch (error) {
        failed++;
        errors.push(`Failed to process ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`);

        if (!continueOnError) {
          break;
        }
      }
    }
  }

  return { processed, failed, errors };
}

/**
 * Base activity processing function to eliminate code duplication
 */
export async function processActivityBase<T extends Activity>(
  activity: T,
  options: {
    requiredFields: (keyof T)[];
    extractUrls: (activity: T) => { actorUri: string; objectUri?: string; targetUri?: string };
    validateActivity?: (activity: T, actors: any) => Promise<boolean>;
    processActivity: (activity: T, actors: any) => Promise<boolean>;
    activityName: string;
  }
): Promise<boolean> {
  try {
    // 1. Validate required fields
    for (const field of options.requiredFields) {
      if (!activity[field]) {
        console.warn(`${options.activityName} activity missing ${String(field)}`);
        return false;
      }
    }

    // 2. Extract URLs from activity
    const urls = options.extractUrls(activity);

    // 3. Discover and import actors
    const actors: any = {
      actorUri: urls.actorUri,
      objectUri: urls.objectUri,
      targetUri: urls.targetUri
    };

    // Always discover the main actor
    const actor = await discoverActor(urls.actorUri);
    if (!actor) {
      console.warn(`Failed to discover actor: ${urls.actorUri}`);
      return false;
    }
    actors.actor = actor;

    // Discover object actor if it's a user URI
    if (urls.objectUri) {
      try {
        const objectActor = await discoverActor(urls.objectUri);
        actors.objectActor = objectActor;
      } catch {
        // Object might not be an actor, that's okay
        actors.objectActor = null;
      }
    }

    // 4. Additional validation if provided
    if (options.validateActivity) {
      const isValid = await options.validateActivity(activity, actors);
      if (!isValid) {
        return false;
      }
    }

    // 5. Process the activity
    return await options.processActivity(activity, actors);

  } catch (error) {
    console.error(`Error processing ${options.activityName} activity:`, error);
    throw error;
  }
}

/**
 * Activity type-specific processors
 */
export const ActivityProcessors = {
  /**
   * Process Create activity (legacy - URL-only approach)
   * @deprecated Use processCreateSmart instead for better handling of embedded objects
   */
  async processCreate(
    activity: Create,
    importPost: (url: string) => Promise<void>
  ): Promise<{ processed: number; failed: number; errors: string[] }> {
    return processActivityObjects(activity, importPost, {
      parallel: true,
      continueOnError: true,
      maxConcurrency: 3
    });
  },

  /**
   * Smart Create activity processor that handles embedded objects and URL references appropriately
   */
  async processCreateSmart(
    activity: Create,
    importEmbeddedPost: (post: any) => Promise<void>,
    importPostFromUrl: (url: string) => Promise<void>
  ): Promise<{
    processed: number;
    failed: number;
    errors: string[];
    embeddedCount: number;
    fetchedCount: number;
  }> {
    return processCreateActivitySmart(
      activity,
      importEmbeddedPost,
      importPostFromUrl
    );
  },

  /**
   * Process Follow activity
   */
  async processFollow(
    activity: Follow,
    importActor: (url: string) => Promise<void>
  ): Promise<{ processed: number; failed: number; errors: string[] }> {
    return processActivityObjects(activity, importActor, {
      parallel: false, // Follow activities should be processed sequentially
      continueOnError: false
    });
  },

  /**
   * Process Like activity
   */
  async processLike(
    activity: Like,
    importPost: (url: string) => Promise<void>
  ): Promise<{ processed: number; failed: number; errors: string[] }> {
    return processActivityObjects(activity, importPost, {
      parallel: true,
      continueOnError: true,
      maxConcurrency: 2
    });
  },

  /**
   * Process Announce activity (boost/share)
   */
  async processAnnounce(
    activity: Announce,
    importPost: (url: string) => Promise<void>
  ): Promise<{ processed: number; failed: number; errors: string[] }> {
    return processActivityObjects(activity, importPost, {
      parallel: true,
      continueOnError: true,
      maxConcurrency: 2
    });
  },

  /**
   * Process Update activity
   */
  async processUpdate(
    activity: Update,
    importPost: (url: string) => Promise<void>
  ): Promise<{ processed: number; failed: number; errors: string[] }> {
    return processActivityObjects(activity, importPost, {
      parallel: true,
      continueOnError: true,
      maxConcurrency: 3
    });
  },

  /**
   * Process Delete activity
   */
  async processDelete(
    activity: Delete,
    deletePost: (url: string) => Promise<void>
  ): Promise<{ processed: number; failed: number; errors: string[] }> {
    return processActivityObjects(activity, deletePost, {
      parallel: true,
      continueOnError: true,
      maxConcurrency: 5
    });
  }
} as const;

/**
 * Smart processing for Create activities that handles both embedded objects and URL references
 */
export async function processCreateActivitySmart(
  activity: Create,
  processEmbeddedObject: (obj: any) => Promise<void>,
  fetchAndProcessUrl: (url: string) => Promise<void>
): Promise<{
  processed: number;
  failed: number;
  errors: string[];
  embeddedCount: number;
  fetchedCount: number;
}> {
  const { embedded, references } = separateEmbeddedAndReferencedObjects(activity);

  let processed = 0;
  let failed = 0;
  const errors: string[] = [];
  let embeddedCount = 0;
  let fetchedCount = 0;

  // Process embedded objects first (these are already available)
  for (const obj of embedded) {
    try {
      await processEmbeddedObject(obj);
      processed++;
      embeddedCount++;
    } catch (error) {
      failed++;
      errors.push(`Failed to process embedded object: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Then process URL references (these need to be fetched)
  for (const url of references) {
    try {
      await fetchAndProcessUrl(url);
      processed++;
      fetchedCount++;
    } catch (error) {
      failed++;
      errors.push(`Failed to fetch and process ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return {
    processed,
    failed,
    errors,
    embeddedCount,
    fetchedCount
  };
}
