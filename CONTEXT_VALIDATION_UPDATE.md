# ActivityPub @context Validation Update

## Обзор изменений

Обновлены типы и валидация для поля `@context` в ActivityPub объектах в соответствии с документацией ActivityPub и Mastodon.

## Проблема

Ранее `@context` был определен как:
- **Типы**: `OrArray<URL | Record<string, URL>>`
- **Валидация**: только строки в массиве

Но согласно документации ActivityPub и Mastodon, `@context` может содержать более сложные структуры JSON-LD.

## Решение

### 1. Обновлены типы

**Файл**: `src/lib/activitypub/types/Core/Entity.ts`

Добавлен новый тип `JsonLdContext`:

```typescript
/**
 * JSON-LD Context can be a string (URL), an object with term definitions,
 * or an array containing both strings and objects.
 */
export type JsonLdContext = string | Record<string, any> | Array<string | Record<string, any>>;
```

Обновлен `BaseEntity`:

```typescript
export type BaseEntity<T extends AnyType> = {
  '@context'?: JsonLdContext;
  // ...
};
```

### 2. Обновлена валидация

**Файл**: `src/lib/activitypub/utils/validation.ts`

- Добавлен новый тип валидации: `'jsonld-context'`
- Добавлена функция `validateJsonLdContext()` с полной валидацией
- Обновлена схема для `@context`: `{ type: 'jsonld-context' }`

### 3. Поддерживаемые форматы @context

#### Строка (URL контекста)
```json
{
  "@context": "https://www.w3.org/ns/activitystreams"
}
```

#### Объект (inline определения)
```json
{
  "@context": {
    "toot": "http://joinmastodon.org/ns#",
    "discoverable": "toot:discoverable"
  }
}
```

#### Массив (смешанные строки и объекты)
```json
{
  "@context": [
    "https://www.w3.org/ns/activitystreams",
    "https://w3id.org/security/v1",
    {
      "toot": "http://joinmastodon.org/ns#",
      "Emoji": "toot:Emoji",
      "discoverable": "toot:discoverable"
    }
  ]
}
```

#### Сложный Mastodon-стиль контекст
```json
{
  "@context": [
    "https://www.w3.org/ns/activitystreams",
    "https://w3id.org/security/v1",
    {
      "manuallyApprovesFollowers": "as:manuallyApprovesFollowers",
      "toot": "http://joinmastodon.org/ns#",
      "featured": {
        "@id": "toot:featured",
        "@type": "@id"
      },
      "featuredTags": {
        "@id": "toot:featuredTags",
        "@type": "@id"
      },
      "schema": "http://schema.org#",
      "PropertyValue": "schema:PropertyValue",
      "value": "schema:value",
      "discoverable": "toot:discoverable"
    }
  ]
}
```

## Валидация

Новая валидация проверяет:

✅ **Допустимые форматы:**
- Строки (не пустые)
- Объекты (не null)
- Массивы со строками и объектами
- Отсутствие поля (опционально)

❌ **Недопустимые форматы:**
- Пустые строки
- `null` значения
- Пустые массивы
- Числа, булевы значения
- Массивы с недопустимыми типами элементов

## Коды ошибок

- `EMPTY_CONTEXT_STRING` - пустая строка в @context
- `NULL_CONTEXT` - null значение в @context
- `EMPTY_CONTEXT_ARRAY` - пустой массив в @context
- `EMPTY_CONTEXT_ARRAY_STRING` - пустая строка в массиве @context
- `INVALID_CONTEXT_ARRAY_ITEM` - недопустимый тип элемента массива
- `INVALID_CONTEXT_TYPE` - недопустимый тип @context

## Тестирование

Добавлены тесты в `src/lib/activitypub/utils/validation.test.ts` для проверки всех сценариев валидации @context.

## Совместимость

Изменения обратно совместимы - все существующие валидные @context продолжат работать, но теперь поддерживаются дополнительные форматы согласно стандарту.

## Ссылки

- [ActivityPub Specification](https://www.w3.org/TR/activitypub/)
- [JSON-LD Context](https://www.w3.org/TR/json-ld/#the-context)
- [Mastodon ActivityPub Documentation](https://docs.joinmastodon.org/spec/activitypub/#contexts)
