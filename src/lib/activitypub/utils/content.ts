/**
 * Content processing utilities for ActivityPub objects
 * Handles Markdown, mentions, hashtags, media, and content sanitization
 */

import type { Mention, ExtendedObject } from '$lib/activitypub/types';
import { escapeHtml } from './security';
import { logger } from './logger';
import { imageSize } from 'image-size';

/**
 * Mention extraction result
 */
export interface MentionMatch {
  text: string;           // Original text like "@<EMAIL>"
  username: string;       // "user"
  domain?: string;        // "domain.com" (undefined for local mentions)
  startIndex: number;     // Position in text
  endIndex: number;       // End position in text
}

/**
 * Hashtag extraction result
 */
export interface HashtagMatch {
  text: string;           // Original text like "#hashtag"
  tag: string;            // "hashtag" (without #)
  startIndex: number;     // Position in text
  endIndex: number;       // End position in text
}

/**
 * Media file information
 */
export interface MediaInfo {
  type: 'image' | 'video' | 'audio' | 'document' | 'unknown';
  mimeType: string;
  size?: number;
  width?: number;
  height?: number;
  duration?: number;
  isValid: boolean;
  errors: string[];
}

/**
 * Content processing options
 */
export interface ContentProcessingOptions {
  allowMarkdown?: boolean;
  extractMentions?: boolean;
  extractHashtags?: boolean;
  sanitizeHtml?: boolean;
  maxLength?: number;
  allowedTags?: string[];
  baseUrl?: string;
}

/**
 * Content processing result
 */
export interface ProcessedContent {
  html: string;
  text: string;
  mentions: MentionMatch[];
  hashtags: HashtagMatch[];
  links: Mention[];
  tags: ExtendedObject[];
  warnings: string[];
  errors: string[];
}

/**
 * Default content processing options
 */
export const DEFAULT_CONTENT_OPTIONS: ContentProcessingOptions = {
  allowMarkdown: true,
  extractMentions: true,
  extractHashtags: true,
  sanitizeHtml: true,
  maxLength: 50000,
  allowedTags: ['p', 'br', 'strong', 'em', 'a', 'ul', 'ol', 'li', 'blockquote', 'code', 'pre'],
  baseUrl: undefined
};

/**
 * Extract mentions from text
 */
export function extractMentions(text: string): MentionMatch[] {
  const mentions: MentionMatch[] = [];
  
  // Find all HTML tags to exclude their content from plain text search
  const htmlTagRanges: Array<{ start: number; end: number }> = [];
  const tagRegex = /<[^>]*>/g;
  let tagMatch;
  while ((tagMatch = tagRegex.exec(text)) !== null) {
    htmlTagRanges.push({
      start: tagMatch.index,
      end: tagMatch.index + tagMatch[0].length
    });
  }
  
  // Pattern for mentions with negative lookbehind/lookahead to avoid URLs
  const mentionRegex = /(?<![\w/.])(@([a-zA-Z0-9_.-]+)(?:@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,}))?|([a-zA-Z0-9_.-]+@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})))(?![\w@.-])/g;
  
  // First, extract plain text mentions (excluding HTML tags)
  let match;
  while ((match = mentionRegex.exec(text)) !== null) {
    const isInsideTag = htmlTagRanges.some(range => 
      match.index >= range.start && match.index < range.end
    );
    
    if (!isInsideTag) {
      let mentionText, username, domain;
      
      if (match[1]) {
        // @username or @username@domain format
        mentionText = match[1];
        username = match[2];
        domain = match[3];
      } else if (match[4]) {
        // username@domain format (without leading @)
        mentionText = match[4];
        username = match[4].split('@')[0];
        domain = match[5];
      }
      
      if (username) {
        mentions.push({
          text: mentionText,
          username,
          domain,
          startIndex: match.index,
          endIndex: match.index + match[0].length
        });
      }
    }
  }
  
  // Then, extract HTML-wrapped mentions
  const htmlLinkRegex = /<a\b[^>]*href\s*=\s*["']([^"']+)["'][^>]*>(.*?)<\/a>/gi;
  
  let htmlMatch;
  while ((htmlMatch = htmlLinkRegex.exec(text)) !== null) {
    const href = htmlMatch[1];
    const linkContent = htmlMatch[2];
    const linkStartIndex = htmlMatch.index;
    
    // Extract text content from potentially nested HTML
    const textContent = linkContent.replace(/<[^>]*>/g, '');
    
    // Check if the text content matches mention pattern (just @username)
    const mentionInHtml = textContent.match(/^@([a-zA-Z0-9_.-]+)$/);
    
    if (mentionInHtml) {
      const fullMatch = htmlMatch[0];
      const username = mentionInHtml[1];
      let domain: string | undefined;
      
      // Extract domain from href if it's external
      try {
        const url = new URL(href);
        if (url.hostname && url.protocol.startsWith('http')) {
          domain = url.hostname;
        }
      } catch {
        // Invalid URL, ignore domain extraction
      }
      
      // Check if this mention position overlaps with any plain text mention
      const overlaps = mentions.some(existing => 
        linkStartIndex < existing.endIndex && 
        (linkStartIndex + fullMatch.length) > existing.startIndex
      );
      
      // Only add if it doesn't overlap with existing plain text mentions
      if (!overlaps) {
        const mentionText = domain ? `@${username}@${domain}` : `@${username}`;
        mentions.push({
          text: mentionText,
          username: username,
          domain: domain,
          startIndex: linkStartIndex,
          endIndex: linkStartIndex + fullMatch.length
        });
      }
    }
  }
  
  // Sort by startIndex to maintain order
  return mentions.sort((a, b) => a.startIndex - b.startIndex);
}

/**
 * Extract hashtags from text
 */
export function extractHashtags(text: string): HashtagMatch[] {
  const hashtags: HashtagMatch[] = [];
  
  // Find all HTML tags and HTML entities to exclude from plain text search
  const excludedRanges: Array<{ start: number; end: number }> = [];
  
  // Find HTML tags
  const tagRegex = /<[^>]*>/g;
  let tagMatch;
  while ((tagMatch = tagRegex.exec(text)) !== null) {
    excludedRanges.push({
      start: tagMatch.index,
      end: tagMatch.index + tagMatch[0].length
    });
  }
  
  // Find HTML entities (&#39;, &amp;, &lt;, etc.)
  const entityRegex = /&(?:#(?:\d+|x[0-9a-fA-F]+)|[a-zA-Z][a-zA-Z0-9]*);/g;
  let entityMatch;
  while ((entityMatch = entityRegex.exec(text)) !== null) {
    excludedRanges.push({
      start: entityMatch.index,
      end: entityMatch.index + entityMatch[0].length
    });
  }
  
  // Pattern for #hashtag (letters, numbers, underscore, including Unicode letters)
  const hashtagRegex = /#([\p{L}\p{N}_][\p{L}\p{N}_]*)/gu;
  
  // First, extract plain text hashtags (excluding HTML tags and entities)
  let match;
  while ((match = hashtagRegex.exec(text)) !== null) {
    const isExcluded = excludedRanges.some(range => 
      match.index >= range.start && match.index < range.end
    );
    
    if (!isExcluded) {
      hashtags.push({
        text: match[0],
        tag: match[1],
        startIndex: match.index,
        endIndex: match.index + match[0].length
      });
    }
  }
  
  // Then, extract HTML-wrapped hashtags
  const htmlLinkRegex = /<a\b[^>]*>(.*?)<\/a>/gi;
  
  let htmlMatch;
  while ((htmlMatch = htmlLinkRegex.exec(text)) !== null) {
    const linkContent = htmlMatch[1];
    const linkStartIndex = htmlMatch.index;
    
    // Extract text content from potentially nested HTML
    const textContent = linkContent.replace(/<[^>]*>/g, '');
    
    // Check if the text content matches hashtag pattern
    const hashtagInHtml = textContent.match(/^#([\p{L}\p{N}_][\p{L}\p{N}_]*)$/u);
    
    if (hashtagInHtml) {
      const fullMatch = htmlMatch[0];
      const tag = hashtagInHtml[1];
      
      // Check if this hashtag position overlaps with any plain text hashtag
      const overlaps = hashtags.some(existing => 
        linkStartIndex < existing.endIndex && 
        (linkStartIndex + fullMatch.length) > existing.startIndex
      );
      
      // Only add if it doesn't overlap with existing plain text hashtags
      if (!overlaps) {
        hashtags.push({
          text: `#${tag}`,
          tag: tag,
          startIndex: linkStartIndex,
          endIndex: linkStartIndex + fullMatch.length
        });
      }
    }
  }
  
  // Sort by startIndex to maintain order
  return hashtags.sort((a, b) => a.startIndex - b.startIndex);
}

/**
 * Convert mentions to ActivityPub Mention objects
 */
export function mentionsToLinks(mentions: MentionMatch[], baseUrl?: string): Mention[] {
  return mentions.map(mention => {
    const href = mention.domain
      ? `https://${mention.domain}/users/${mention.username}`
      : baseUrl
        ? `${baseUrl}/users/${mention.username}`
        : `/@${mention.username}`;

    return {
      type: 'Mention',
      href: new URL(href),
      name: mention.text
    };
  });
}

/**
 * Convert hashtags to ActivityPub Hashtag objects
 */
export function hashtagsToTags(hashtags: HashtagMatch[], baseUrl?: string): ExtendedObject[] {
  return hashtags.map(hashtag => {
    const href = baseUrl
      ? `${baseUrl}/tags/${hashtag.tag.toLowerCase()}`
      : `#${hashtag.tag}`;

    return {
      type: 'Hashtag',
      href: new URL(href),
      name: hashtag.text
    };
  });
}

/**
 * Basic Markdown to HTML conversion
 */
export function markdownToHtml(markdown: string): string {
  let html = markdown;
  
  // Headers
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  
  // Bold
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');
  
  // Italic
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  html = html.replace(/_(.*?)_/g, '<em>$1</em>');
  
  // Code blocks
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
  
  // Inline code
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');
  
  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
  
  // Line breaks
  html = html.replace(/\n\n/g, '</p><p>');
  html = html.replace(/\n/g, '<br>');
  
  // Wrap in paragraphs
  if (html && !html.startsWith('<')) {
    html = '<p>' + html + '</p>';
  }
  
  return html;
}

/**
 * Sanitize HTML content while preserving allowed tags
 */
export function sanitizeHtml(html: string, allowedTags: string[] = DEFAULT_CONTENT_OPTIONS.allowedTags!): string {
  // Basic implementation - in production, use a proper HTML sanitizer library
  let sanitized = html;
  
  // Remove script tags and their content
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove dangerous attributes
  sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
  sanitized = sanitized.replace(/\s*javascript\s*:/gi, '');
  
  // Remove style attributes (basic XSS prevention)
  sanitized = sanitized.replace(/\s*style\s*=\s*["'][^"']*["']/gi, '');
  
  // Allow only specified tags (very basic implementation)
  const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>/g;
  sanitized = sanitized.replace(tagRegex, (match, tagName) => {
    if (allowedTags.includes(tagName.toLowerCase())) {
      return match;
    }
    return '';
  });
  
  return sanitized;
}

/**
 * Get media type from MIME type
 */
export function getMediaType(mimeType: string): MediaInfo['type'] {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.startsWith('text/') || mimeType.includes('pdf') || mimeType.includes('document')) {
    return 'document';
  }
  return 'unknown';
}

/**
 * Validate media file
 */
export function validateMediaFile(
  mimeType: string, 
  size?: number, 
  width?: number, 
  height?: number,
  duration?: number
): MediaInfo {
  const errors: string[] = [];
  const type = getMediaType(mimeType);
  
  // Size limits (in bytes)
  const sizeLimits = {
    image: 10 * 1024 * 1024,    // 10MB
    video: 100 * 1024 * 1024,   // 100MB
    audio: 50 * 1024 * 1024,    // 50MB
    document: 20 * 1024 * 1024, // 20MB
    unknown: 5 * 1024 * 1024    // 5MB
  };
  
  // Check file size
  if (size !== undefined) {
    const limit = sizeLimits[type];
    if (size > limit) {
      errors.push(`File size ${size} exceeds limit ${limit} for ${type} files`);
    }
  }
  
  // Check image dimensions
  if (type === 'image' && width !== undefined && height !== undefined) {
    const maxDimension = 4096;
    if (width > maxDimension || height > maxDimension) {
      errors.push(`Image dimensions ${width}x${height} exceed maximum ${maxDimension}x${maxDimension}`);
    }
  }
  
  // Check video/audio duration
  if ((type === 'video' || type === 'audio') && duration !== undefined) {
    const maxDuration = type === 'video' ? 3600 : 7200; // 1 hour for video, 2 hours for audio
    if (duration > maxDuration) {
      errors.push(`${type} duration ${duration}s exceeds maximum ${maxDuration}s`);
    }
  }
  
  // Check MIME type whitelist
  const allowedMimeTypes = [
    // Images
    'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    // Videos
    'video/mp4', 'video/webm', 'video/ogg',
    // Audio
    'audio/mp3', 'audio/ogg', 'audio/wav', 'audio/m4a',
    // Documents
    'text/plain', 'application/pdf'
  ];
  
  if (!allowedMimeTypes.includes(mimeType)) {
    errors.push(`MIME type ${mimeType} is not allowed`);
  }
  
  return {
    type,
    mimeType,
    size,
    width,
    height,
    duration,
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Process content with all available features
 */
export function processContent(
  content: string, 
  options: ContentProcessingOptions = DEFAULT_CONTENT_OPTIONS
): ProcessedContent {
  const opts = { ...DEFAULT_CONTENT_OPTIONS, ...options };
  const warnings: string[] = [];
  const errors: string[] = [];
  
  let processedText = content;
  let processedHtml = content;
  
  // Check content length
  if (opts.maxLength && content.length > opts.maxLength) {
    errors.push(`Content length ${content.length} exceeds maximum ${opts.maxLength}`);
    processedText = content.substring(0, opts.maxLength);
    processedHtml = processedText;
  }
  

  
  // Extract mentions and hashtags
  const mentions = opts.extractMentions ? extractMentions(processedText) : [];
  const hashtags = opts.extractHashtags ? extractHashtags(processedText) : [];
  
  // Convert to ActivityPub objects
  const links = mentionsToLinks(mentions, opts.baseUrl);
  const tags = hashtagsToTags(hashtags, opts.baseUrl);
  
  // Process Markdown
  if (opts.allowMarkdown) {
    processedHtml = markdownToHtml(processedText);
  }
  
  // Sanitize HTML
  if (opts.sanitizeHtml) {
    processedHtml = sanitizeHtml(processedHtml, opts.allowedTags);
  }
  
  // Log processing
  logger.debug('Content processed', {
    operation: 'content.processContent',
    metadata: {
      originalLength: content.length,
      processedLength: processedText.length,
      mentionsCount: mentions.length,
      hashtagsCount: hashtags.length,
      hasWarnings: warnings.length > 0,
      hasErrors: errors.length > 0
    }
  });
  
  return {
    html: processedHtml,
    text: processedText,
    mentions,
    hashtags,
    links,
    tags,
    warnings,
    errors
  };
}

/**
 * Extract URLs from text
 */
export function extractUrls(text: string): string[] {
  const urlRegex = /https?:\/\/[^\s<>"{}|\\^`[\]]+/g;
  return text.match(urlRegex) || [];
}

/**
 * Replace mentions and hashtags with HTML links
 */
export function linkifyContent(
  content: string,
  mentions: MentionMatch[],
  hashtags: HashtagMatch[],
  baseUrl?: string
): string {
  let result = content;

  // Sort by position (descending) to avoid index shifting
  const allMatches = [
    ...mentions.map(m => ({ ...m, type: 'mention' as const })),
    ...hashtags.map(h => ({ ...h, type: 'hashtag' as const }))
  ].sort((a, b) => b.startIndex - a.startIndex);

  for (const match of allMatches) {
    const before = result.substring(0, match.startIndex);
    const after = result.substring(match.endIndex);

    let replacement: string;
    if (match.type === 'mention') {
      const mention = match as MentionMatch & { type: 'mention' };
      const href = mention.domain
        ? `https://${mention.domain}/users/${mention.username}`
        : baseUrl
          ? `${baseUrl}/users/${mention.username}`
          : `/@${mention.username}`;
      replacement = `<a href="${escapeHtml(href)}" class="mention">@${escapeHtml(mention.username)}${mention.domain ? '@' + escapeHtml(mention.domain) : ''}</a>`;
    } else {
      const hashtag = match as HashtagMatch & { type: 'hashtag' };
      const href = baseUrl
        ? `${baseUrl}/tags/${hashtag.tag.toLowerCase()}`
        : `#${hashtag.tag}`;
      replacement = `<a href="${escapeHtml(href)}" class="hashtag">#${escapeHtml(hashtag.tag)}</a>`;
    }

    result = before + replacement + after;
  }

  return result;
}

/**
 * Create media attachment object
 */
export function createMediaAttachment(
  url: string,
  mediaInfo: MediaInfo,
  name?: string,
  blurhash?: string
) {
  return {
    type: 'Document',
    mediaType: mediaInfo.mimeType,
    url,
    name: name || `${mediaInfo.type} attachment`,
    width: mediaInfo.width,
    height: mediaInfo.height,
    duration: mediaInfo.duration,
    blurhash
  };
}

/**
 * Validate and process multiple media files
 */
export function processMediaFiles(files: Array<{
  url: string;
  mimeType: string;
  size?: number;
  width?: number;
  height?: number;
  duration?: number;
  name?: string;
}>): {
  valid: Array<ReturnType<typeof createMediaAttachment>>;
  invalid: Array<{ file: typeof files[0]; errors: string[] }>;
  warnings: string[];
} {
  const valid = [];
  const invalid = [];
  const warnings = [];

  const maxFiles = 4; // ActivityPub recommendation

  if (files.length > maxFiles) {
    warnings.push(`Too many media files: ${files.length} > ${maxFiles}`);
  }

  for (const file of files.slice(0, maxFiles)) {
    const mediaInfo = validateMediaFile(
      file.mimeType,
      file.size,
      file.width,
      file.height,
      file.duration
    );

    if (mediaInfo.isValid) {
      valid.push(createMediaAttachment(file.url, mediaInfo, file.name));
    } else {
      invalid.push({ file, errors: mediaInfo.errors });
    }
  }

  return { valid, invalid, warnings };
}

/**
 * Clean and normalize text content
 */
export function normalizeTextContent(text: string): string {
  return text
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Remove leading/trailing whitespace
    .trim()
    // Remove null bytes and control characters
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    // Normalize Unicode
    .normalize('NFC');
}

/**
 * Truncate content while preserving word boundaries
 */
export function truncateContent(content: string, maxLength: number, suffix = '...'): string {
  if (content.length <= maxLength) {
    return content;
  }

  const truncated = content.substring(0, maxLength - suffix.length);
  const lastSpace = truncated.lastIndexOf(' ');

  if (lastSpace > 0 && lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + suffix;
  }

  return truncated + suffix;
}

/**
 * Count content statistics
 */
export function getContentStats(content: string): {
  characters: number;
  words: number;
  lines: number;
  mentions: number;
  hashtags: number;
  urls: number;
} {
  const mentions = extractMentions(content);
  const hashtags = extractHashtags(content);
  const urls = extractUrls(content);

  return {
    characters: content.length,
    words: content.trim().split(/\s+/).filter(word => word.length > 0).length,
    lines: content.split('\n').length,
    mentions: mentions.length,
    hashtags: hashtags.length,
    urls: urls.length
  };
}

/**
 * Check if content needs content warning
 */
export function needsContentWarning(content: string): { needed: boolean; reasons: string[] } {
  const reasons: string[] = [];
  const lowerContent = content.toLowerCase();

  // Check for sensitive topics (basic implementation)
  const sensitivePatterns = [
    /\b(death|suicide|self.?harm)\b/i,
    /\b(violence|abuse|assault)\b/i,
    /\b(nsfw|explicit|adult)\b/i,
    /\b(drug|alcohol|substance)\b/i
  ];

  for (const pattern of sensitivePatterns) {
    if (pattern.test(lowerContent)) {
      reasons.push(`Contains potentially sensitive content: ${pattern.source}`);
    }
  }

  // Check for excessive caps
  const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
  if (capsRatio > 0.5 && content.length > 20) {
    reasons.push('Excessive use of capital letters');
  }

  return {
    needed: reasons.length > 0,
    reasons
  };
}

/**
 * Emoji to Lucide icon mapping
 */
const EMOJI_TO_LUCIDE_MAP: Record<string, string> = {
  // Emotions & People
  '❤️': 'Heart',
  '💔': 'HeartCrack',
  '👍': 'ThumbsUp',
  '👎': 'ThumbsDown',

  // Objects & Symbols
  '⭐': 'Star',
  '🌟': 'Sparkles',
  '✨': 'Sparkles',
  '🔥': 'Flame',
  '💡': 'Lightbulb',
  '💻': 'Laptop',
  '📱': 'Smartphone',
  '📞': 'Phone',
  '📧': 'Mail',
  '✉️': 'Mail',
  '📨': 'Mail',
  '📩': 'Mail',
  '📮': 'Mail',
  '🏠': 'House',
  '🏡': 'House',
  '🚗': 'Car',
  '🚙': 'Car',
  '🚕': 'Taxi',
  '✈️': 'Plane',
  '🛩️': 'Plane',
  '🚀': 'Rocket',
  '🔒': 'Lock',
  '🔓': 'LockOpen',
  '🔑': 'KeyRound',
  '⚙️': 'Settings',
  '🔧': 'Wrench',
  '🔨': 'Hammer',
  '🔍': 'Search',
  '🔎': 'Search',
  '➕': 'Plus',
  '➖': 'Minus',
  '✅': 'SquareCheckBig',
  '☑️': 'SquareCheckBig',
  '✔️': 'Check',
  '❌': 'X',
  '❎': 'X',
  '⚠️': 'TriangleAlert',
  '⚡': 'Zap',
  '🔋': 'BatteryCharging',
  '🔌': 'Plug',
  'ℹ️': 'BadgeInfo',
  '❓': 'CircleQuestionMark',
  '❔': 'CircleQuestionMark',
  '❗': 'CircleAlert',
  '❕': 'CircleAlert',

  // Time & Calendar
  '📅': 'Calendar',
  '📆': 'Calendar',
  '🗓️': 'Calendar',
  '🕐': 'Clock1',
  '🕑': 'Clock2',
  '🕒': 'Clock3',
  '🕓': 'Clock4',
  '🕔': 'Clock5',
  '🕕': 'Clock6',
  '🕖': 'Clock7',
  '🕗': 'Clock8',
  '🕘': 'Clock9',
  '🕙': 'Clock10',
  '🕚': 'Clock11',
  '🕛': 'Clock12',
  '⏰': 'AlarmClock',
  '⏱️': 'Timer',
  '⏲️': 'Timer',

  // Files & Folders
  '📁': 'Folder',
  '📂': 'FolderOpen',
  '📄': 'FileText',
  '📃': 'FileText',
  '📋': 'Clipboard',
  '📊': 'ChartColumn',
  '📈': 'TrendingUp',
  '📉': 'TrendingDown',
  '💾': 'Save',
  '🗑️': 'Trash',
  '🗂️': 'Folder',

  // Media & Entertainment
  '📷': 'Camera',
  '📸': 'Camera',
  '📹': 'Video',
  '🎵': 'Music',
  '🎶': 'Music',
  '🎧': 'Headphones',
  '🎮': 'Gamepad2',
  '🎯': 'Target',
  '🎨': 'Palette',
  '🖼️': 'Image',
  '🎬': 'Clapperboard',

  // Weather & Nature
  '☀️': 'Sun',
  '🌞': 'Sun',
  '🌙': 'Moon',
  '🌛': 'Moon',
  '🌜': 'Moon',
  '☁️': 'Cloud',
  '⛅': 'Cloud',
  '🌤️': 'CloudSun',
  '⛈️': 'CloudLightning',
  '🌧️': 'CloudRain',
  '❄️': 'Snowflake',
  '🌈': 'Rainbow',
  '🌍': 'Earth',
  '🌎': 'Earth',
  '🌏': 'Earth',
  '🌳': 'TreeDeciduous',
  '🌲': 'TreePine',
  '🌱': 'Sprout',

  // Food & Drink
  '☕': 'Coffee',
  '🍕': 'Pizza',
  '🍔': 'Hamburger',
  '🍎': 'Apple',
  '🍌': 'Banana',
  '🍇': 'Grape',
  '🍓': 'Cherry',
  '🥕': 'Carrot',

  // Transportation
  '🚲': 'Bike',
  '🚌': 'Bus',
  '🚊': 'TrainFront',
  '🚇': 'TrainFront',
  '⛵': 'Sailboat',
  '🚢': 'Ship',

  // Arrows & Directions
  '⬆️': 'SquareArrowUp',
  '⬇️': 'SquareArrowDown',
  '⬅️': 'SquareArrowLeft',
  '➡️': 'SquareArrowRight',
  '↗️': 'SquareArrowUpRight',
  '↘️': 'SquareArrowDownRight',
  '↙️': 'SquareArrowDownLeft',
  '↖️': 'SquareArrowUpLeft',
  '🔄': 'RefreshCw',
  '🔃': 'RefreshCcw',
  '🔁': 'Repeat',
  '🔀': 'Shuffle',

  // Money & Shopping
  '💰': 'DollarSign',
  '💳': 'CreditCard',
  '🛒': 'ShoppingCart',
  '🛍️': 'ShoppingBag',
  '💎': 'Gem',
  '🏆': 'Trophy',
  '🎁': 'Gift',

  // Health & Medical
  '💊': 'Pill',
  '🩺': 'Stethoscope',
  '🏥': 'Hospital',
  '🚑': 'Ambulance',
  '⚕️': 'Cross',

  // Sports & Activities
  '🚴': 'Bike',

  // Miscellaneous
  '🔗': 'Link',
  '📎': 'Paperclip',
  '🔖': 'Bookmark',
  '📌': 'Pin',
  '📍': 'MapPin',
  '🔔': 'Bell',
  '🔕': 'BellOff',
  '📢': 'Megaphone',
  '📣': 'Megaphone',
  '💬': 'MessageCircle',
  '🗨️': 'MessageSquare',
  '🔬': 'Microscope',
  '🔭': 'Telescope',
  '🛰️': 'Satellite',
  '🌐': 'Globe',
  '⚖️': 'Scale',
  '🗝️': 'Key',
  '🛡️': 'Shield',
  '⚔️': 'Swords',
  '🏹': 'BowArrow',
  '🧭': 'Compass',
  '🗺️': 'Map',
  '🏔️': 'MountainSnow',
  '🏕️': 'TentTree',
  '🗻': 'MountainSnow',
  '🎢': 'RollerCoaster',
  '🎡': 'FerrisWheel',

  // Added by hand
  '#️⃣': 'Hash',
  '🛠️': 'Wrench',
  '📚': 'LibraryBig',
  '⌚': 'Watch'
};

/**
 * Replace emoji with Lucide icons
 *
 * This function finds emoji in text and replaces them with corresponding Lucide icon web components.
 * The replacement uses a custom web component that will render the appropriate Lucide icon.
 *
 * @param text - Text containing emoji to replace
 * @returns Text with emoji replaced by Lucide icon components
 */
export function replaceEmojiWithLucideIcons(text: string): string {
  let result = text;

  // Sort emoji by length (longest first) to avoid partial replacements
  const sortedEmoji = Object.keys(EMOJI_TO_LUCIDE_MAP).sort((a, b) => b.length - a.length);

  for (const emoji of sortedEmoji) {
    const iconName = EMOJI_TO_LUCIDE_MAP[emoji];
    // Replace emoji with custom web component
    const replacement = `<epos-lucide-icon icon="${iconName}" title="${emoji}"></epos-lucide-icon>`;
    result = result.replaceAll(emoji, replacement);
  }

  return result;
}

/**
 * Transform HTML links to add security attributes and format URL display
 *
 * This function:
 * 1. Removes all original attributes except href
 * 2. Adds rel="nofollow noopener" and target="_blank" attributes
 * 3. If innerHTML contains a URL (even with HTML tags), replaces it with formatted href content:
 *    - Removes http(s):// protocol from display
 *    - Wraps domain in a span with class "link_host"
 *
 * @param html - HTML string containing links to transform
 * @returns Transformed HTML string with processed links
 */
export function transformHtmlLinks(html: string): string {
  // Regular expression to find all <a> tags
  const linkRegex = /<a\b[^>]*>(.*?)<\/a>/gi;

  return html.replace(linkRegex, (match, innerHTML) => {
    // Extract href from the original tag
    const hrefMatch = match.match(/href\s*=\s*["']([^"']+)["']/i);
    if (!hrefMatch) return match; // If no href, leave as is

    const href = hrefMatch[1];
    let linkText = innerHTML.trim();

    // Extract plain text from innerHTML (removing HTML tags)
    const plainText = innerHTML.replace(/<[^>]*>/g, '').trim();

    // Check if plain text is a URL
    const urlRegex = /^https?:\/\/[^\s<>"{}|\\^`[\]]+$/i;
    if (urlRegex.test(plainText)) {
      // If plain text is a URL, replace it with formatted href content
      try {
        const url = new URL(href);
        const domain = url.hostname;
        let pathAndQuery = url.pathname + url.search + url.hash;

        // Trim trailing slash from path if it's just '/'
        if (pathAndQuery === '/') {
          pathAndQuery = '';
        } else if (pathAndQuery.endsWith('/') && pathAndQuery.length > 1) {
          pathAndQuery = pathAndQuery.slice(0, -1);
        }

        // Create new link text with domain highlighted
        linkText = `<span class="link_host">${domain}</span>${pathAndQuery}`;
      } catch {
        // If URL parsing fails, use href as is, removing protocol and trailing slash
        linkText = href.replace(/^https?:\/\//, '').replace(/\/$/, '');
      }
    }

    // Create new tag with required attributes
    return `<a href="${href}" rel="nofollow noopener" target="_blank">${linkText}</a>`;
  });
}

export async function getImageSize(url: string | URL): Promise<{ width: number; height: number }> {
  const response = await fetch(url.toString(), {
    headers: {
      'Accept': 'image/*'
    }
  });
  const blob = await response.blob();
  const buffer = Buffer.from(await blob.arrayBuffer());
  const dimensions = imageSize(buffer);
  return {
    width: dimensions.width,
    height: dimensions.height
  };
}
