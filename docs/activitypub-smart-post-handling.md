# Smart Post Handling for ActivityPub Create Activities

## Overview

This document describes the enhanced smart post handling logic implemented for ActivityPub Create activities in FireShark. The new system intelligently handles both embedded objects and URL references, with proper authentication for private posts.

## Problem Statement

Previously, the system would always attempt to fetch posts by URL, even when the complete post object was already embedded in the Create activity. This caused issues with:

1. **Private posts**: Servers would return 404/403 errors when trying to fetch private posts without proper authentication
2. **Inefficiency**: Unnecessary network requests for data that was already available
3. **Reliability**: Failed fetches would prevent processing of otherwise valid activities

## Solution: Smart Post Handling

### Key Components

#### 1. Object Type Detection (`isEmbeddedObject`)

```typescript
export function isEmbeddedObject(obj: any): boolean
```

Determines whether an object is a complete embedded object or just a reference:

- **Embedded objects** have `type` + (`content` OR `attributedTo` OR `actor`)
- **References** are URLs or incomplete objects with only `id`

#### 2. Object Separation (`separateEmbeddedAndReferencedObjects`)

```typescript
export function separateEmbeddedAndReferencedObjects(activity: Activity): {
  embedded: any[];
  references: string[];
}
```

Separates Create activity objects into two categories:
- **Embedded**: Complete objects that can be processed immediately
- **References**: URLs that need to be fetched

#### 3. Smart Create Processor (`processCreateActivitySmart`)

```typescript
export async function processCreateActivitySmart(
  activity: Create,
  processEmbeddedObject: (obj: any) => Promise<void>,
  fetchAndProcessUrl: (url: string) => Promise<void>
): Promise<ProcessingResult>
```

Processes Create activities intelligently:
1. Process embedded objects directly (no network requests)
2. Fetch URL references with authentication
3. Provide detailed error handling and logging

#### 4. Authenticated Post Fetching (`importPostWithAuth`)

```typescript
export async function importPostWithAuth(url: string): Promise<any>
```

Enhanced post import with HTTP signature authentication:
- Uses service account for signing requests
- Handles 404/403 errors gracefully
- Provides detailed error messages for debugging

## ActivityPub Compliance

### Specification Adherence

The implementation follows ActivityPub specification guidelines:

1. **Object Embedding**: Supports both embedded objects and URL references as per ActivityStreams 2.0
2. **HTTP Signatures**: Uses HTTP signatures for server-to-server authentication (draft-cavage-http-signatures-08)
3. **Authorized Fetch**: Handles servers that require authentication for all GET requests
4. **Error Handling**: Graceful handling of inaccessible objects as recommended

### Private Post Handling

According to ActivityPub best practices:

- **404 errors**: Post may be deleted or private
- **403 errors**: Access forbidden, likely private post or server restriction
- **401 errors**: Authentication failed, signature issues
- **5xx errors**: Server-side issues, may be retryable

The system logs these scenarios appropriately and continues processing other objects.

## Usage Examples

### Basic Create Activity with Embedded Object

```json
{
  "@context": "https://www.w3.org/ns/activitystreams",
  "type": "Create",
  "actor": "https://example.com/users/alice",
  "object": {
    "type": "Note",
    "id": "https://example.com/notes/1",
    "content": "Hello world!",
    "attributedTo": "https://example.com/users/alice",
    "published": "2023-01-01T00:00:00Z"
  }
}
```

**Result**: Object processed directly, no network request needed.

### Create Activity with URL Reference

```json
{
  "@context": "https://www.w3.org/ns/activitystreams",
  "type": "Create",
  "actor": "https://example.com/users/alice",
  "object": "https://example.com/notes/1"
}
```

**Result**: URL fetched with HTTP signature authentication.

### Mixed Create Activity

```json
{
  "@context": "https://www.w3.org/ns/activitystreams",
  "type": "Create",
  "actor": "https://example.com/users/alice",
  "object": [
    {
      "type": "Note",
      "content": "Embedded note",
      "attributedTo": "https://example.com/users/alice"
    },
    "https://example.com/notes/2"
  ]
}
```

**Result**: First object processed directly, second object fetched with authentication.

## Error Handling

### Embedded Object Processing Errors

- Database connection issues
- Invalid object structure
- Missing required fields

### URL Fetch Errors

- **404**: Post not found or private
- **403**: Access forbidden
- **401**: Authentication failed
- **5xx**: Server errors (retryable)
- **Network**: Timeouts, connection issues

### Logging

The system provides comprehensive logging:

```typescript
// Success
console.log(`✅ Successfully processed embedded Note object: ${objectId}`);

// Errors
console.error(`❌ Failed to import post from ${url}: ${errorMsg}`);

// Summary
console.log(`📊 Processing summary: ${embeddedCount} embedded objects, ${fetchedCount} fetched from URLs`);
```

## Configuration

### Service Account

Ensure a service account exists for HTTP signature authentication:

```bash
npm run create-service-account
```

### Federation Config

HTTP signature settings in federation configuration:

```typescript
{
  httpSignature: {
    algorithm: 'rsa-sha256',
    headers: ['(request-target)', 'host', 'date', 'digest']
  }
}
```

## Migration from Legacy System

The new system is backward compatible:

1. **Legacy function**: `processCreateActivity` (deprecated)
2. **New function**: `processCreateActivitySmart` (recommended)
3. **Automatic**: Main inbox processing now uses smart handler

## Performance Benefits

1. **Reduced network requests**: Embedded objects processed directly
2. **Better error handling**: Graceful handling of private posts
3. **Improved reliability**: Continues processing even if some objects fail
4. **Enhanced logging**: Better debugging and monitoring

## Security Considerations

1. **HTTP Signatures**: All outgoing requests signed with service account
2. **Input validation**: Embedded objects validated before processing
3. **Error disclosure**: Careful not to leak sensitive information in error messages
4. **Rate limiting**: Existing rate limiting applies to URL fetches

## Future Enhancements

1. **Retry logic**: Implement exponential backoff for failed fetches
2. **Caching**: Cache fetched objects to avoid duplicate requests
3. **Metrics**: Add metrics for monitoring processing success rates
4. **Configuration**: Make object detection criteria configurable
