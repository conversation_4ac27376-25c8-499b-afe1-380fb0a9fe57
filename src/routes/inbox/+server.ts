import { validateInboxRequest, createInboxProcessingContext } from '$lib/activitypub/utils/inbox-validation';
import { processActivityObject } from '$lib/activitypub/activities';
import { ActivityPubLogs } from '$lib/activitypub/utils/logger';
import type { ActivityProcessingResult, ActivityProcessingContext } from '$lib/activitypub/types/federation';
import type { Activity } from '$lib/activitypub/types';
import type { RequestHandler } from './$types';

/**
 * Process activity for shared inbox with proper result format
 */
async function processSharedInboxActivity(activity: Activity, context: ActivityProcessingContext): Promise<ActivityProcessingResult> {
  try {
    // processActivityObject returns the processed activity and throws on error
    const processedActivity = await processActivityObject(activity);

    return {
      success: true,
      status: 'completed',
      activityId: processedActivity.id?.toString()
    };
  } catch (error) {
    // Determine if error is retryable based on error type
    const shouldRetry = isRetryableError(error);

    return {
      success: false,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      shouldRetry
    };
  }
}

/**
 * Determine if an error is retryable
 */
function isRetryableError(error: unknown): boolean {
  if (error instanceof Error) {
    // Network errors, timeouts, and temporary server errors are retryable
    return error.message.includes('timeout') ||
           error.message.includes('network') ||
           error.message.includes('temporary') ||
           error.message.includes('rate limit');
  }
  return false;
}

/**
 * Shared inbox endpoint for ActivityPub federation
 * This endpoint receives activities for all users on this server
 */
export const POST: RequestHandler = async ({ request, url }) => {
  console.log('Shared inbox request received');
  try {
    // Extract request details
    const method = request.method;
    const headers: Record<string, string> = {};

    request.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });

    const rawBody = await request.text();
    const sourceIp = headers['x-forwarded-for'] || headers['x-real-ip'];

    // Validate the incoming request
    const validation = await validateInboxRequest(
      method,
      url.toString(),
      headers,
      rawBody,
      sourceIp
    );

    console.log('Preparing to validate shared inbox request');

    if (!validation.valid) {
      ActivityPubLogs.federation.incomingError(
        method,
        url.toString(),
        validation.error || 'Validation failed'
      );

      return new Response(validation.error || 'Invalid request', {
        status: validation.statusCode || 400
      });
    }

    // Create processing context
    const context = createInboxProcessingContext(
      validation.activity!,
      validation.signature,
      rawBody,
      sourceIp
    );

    ActivityPubLogs.federation.incomingRequest(method, url.toString());

    // Process the activity
    const result = await processSharedInboxActivity(validation.activity!, context);

    if (result.success) {
      ActivityPubLogs.federation.incomingResponse(method, url.toString(), 200);
      return new Response('OK', { status: 200 });
    } else {
      ActivityPubLogs.federation.incomingError(
        method,
        url.toString(),
        result.error || 'Processing failed'
      );

      return new Response(result.error || 'Processing failed', {
        status: result.shouldRetry ? 500 : 400
      });
    }

  } catch (error) {
    console.error('Error processing shared inbox request:', error);
    ActivityPubLogs.federation.incomingError(
      'POST',
      url.toString(),
      error instanceof Error ? error.message : 'Unknown error'
    );

    return new Response('Internal Server Error', { status: 500 });
  }
};

/**
 * Handle OPTIONS requests for CORS
 */
export const OPTIONS: RequestHandler = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Accept, Content-Type, Authorization, Signature, Date, Host, Digest',
      'Access-Control-Max-Age': '86400'
    }
  });
};
